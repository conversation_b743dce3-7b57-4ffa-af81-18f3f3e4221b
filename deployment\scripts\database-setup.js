#!/usr/bin/env node

/**
 * Database Setup Script
 * Initializes database schema and runs migrations
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}[${new Date().toISOString()}] ${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function checkDatabaseConnection() {
  info('Checking database connection...');
  
  try {
    // Test database connection using Prisma
    execSync('npx prisma db pull --force', { 
      stdio: 'pipe',
      timeout: 30000 
    });
    success('Database connection verified');
    return true;
  } catch (err) {
    // If db pull fails, try a simple connection test
    try {
      execSync('npx prisma db execute --stdin', { 
        input: 'SELECT 1;',
        stdio: 'pipe',
        timeout: 10000 
      });
      success('Database connection verified (basic test)');
      return true;
    } catch (innerErr) {
      error('Database connection failed');
      console.error('Connection error details:', err.message);
      return false;
    }
  }
}

async function runDatabaseMigrations() {
  info('Running database migrations...');
  
  try {
    // Deploy migrations to production database
    execSync('npx prisma migrate deploy', { 
      stdio: 'inherit',
      timeout: 120000 
    });
    success('Database migrations completed successfully');
    return true;
  } catch (err) {
    warning('Migration deployment failed, attempting to push schema...');
    
    try {
      // If migrations fail, try to push the schema directly
      execSync('npx prisma db push --force-reset', { 
        stdio: 'inherit',
        timeout: 120000 
      });
      success('Database schema pushed successfully');
      return true;
    } catch (pushErr) {
      error('Failed to update database schema');
      console.error('Schema error details:', pushErr.message);
      return false;
    }
  }
}

async function generatePrismaClient() {
  info('Generating Prisma client...');
  
  try {
    execSync('npx prisma generate', { 
      stdio: 'inherit',
      timeout: 60000 
    });
    success('Prisma client generated successfully');
    return true;
  } catch (err) {
    error('Failed to generate Prisma client');
    console.error('Generation error details:', err.message);
    return false;
  }
}

async function seedDatabase() {
  info('Checking for database seed script...');
  
  const seedPath = path.join(process.cwd(), 'prisma', 'seed.js');
  const seedTsPath = path.join(process.cwd(), 'prisma', 'seed.ts');
  
  if (fs.existsSync(seedPath) || fs.existsSync(seedTsPath)) {
    try {
      info('Running database seed...');
      execSync('npx prisma db seed', { 
        stdio: 'inherit',
        timeout: 120000 
      });
      success('Database seeded successfully');
      return true;
    } catch (err) {
      warning('Database seeding failed (this may be normal if data already exists)');
      console.warn('Seed error details:', err.message);
      return false;
    }
  } else {
    info('No seed script found, skipping database seeding');
    return true;
  }
}

async function createInitialAdminUser() {
  info('Checking for admin user creation...');
  
  try {
    // Check if we have admin credentials in environment
    if (process.env.ADMIN_EMAIL && process.env.ADMIN_PASSWORD) {
      // This would typically be handled by your application's user creation logic
      // For now, we'll just log that admin credentials are configured
      success('Admin credentials configured in environment');
      info(`Admin email: ${process.env.ADMIN_EMAIL}`);
      return true;
    } else {
      warning('Admin credentials not found in environment variables');
      info('Please set ADMIN_EMAIL and ADMIN_PASSWORD in your .env file');
      return false;
    }
  } catch (err) {
    warning('Could not verify admin user setup');
    return false;
  }
}

async function validateDatabaseSetup() {
  info('Validating database setup...');
  
  try {
    // Check if we can query the database
    const result = execSync('npx prisma db execute --stdin', { 
      input: 'SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = current_schema();',
      encoding: 'utf8',
      timeout: 10000 
    });
    
    success('Database validation completed');
    info('Database is ready for use');
    return true;
  } catch (err) {
    error('Database validation failed');
    console.error('Validation error details:', err.message);
    return false;
  }
}

async function main() {
  console.log('\n' + '='.repeat(60));
  console.log('🗄️  INTERLOCK AI - DATABASE SETUP');
  console.log('='.repeat(60));
  
  let setupSuccess = true;
  
  try {
    // Step 1: Check database connection
    if (!await checkDatabaseConnection()) {
      setupSuccess = false;
    }
    
    // Step 2: Generate Prisma client
    if (!await generatePrismaClient()) {
      setupSuccess = false;
    }
    
    // Step 3: Run migrations
    if (!await runDatabaseMigrations()) {
      setupSuccess = false;
    }
    
    // Step 4: Seed database (optional)
    await seedDatabase(); // Don't fail setup if seeding fails
    
    // Step 5: Create admin user (optional)
    await createInitialAdminUser(); // Don't fail setup if admin creation fails
    
    // Step 6: Validate setup
    if (!await validateDatabaseSetup()) {
      setupSuccess = false;
    }
    
    console.log('\n' + '='.repeat(60));
    if (setupSuccess) {
      success('🎉 Database setup completed successfully!');
    } else {
      warning('⚠️  Database setup completed with some issues');
    }
    console.log('='.repeat(60));
    
  } catch (err) {
    error(`Database setup failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the database setup
main().catch(err => {
  error(`Database setup process failed: ${err.message}`);
  process.exit(1);
});
