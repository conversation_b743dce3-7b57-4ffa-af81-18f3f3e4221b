#!/bin/bash

# Interlock AI Production Deployment Script
# Version: 1.0.0
# Description: Automated deployment script for cPanel hosting

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEPLOYMENT_DIR="$(pwd)"
APP_DIR="$HOME/public_html"
BACKUP_DIR="$HOME/backups/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$DEPLOYMENT_DIR/deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

# Banner
echo -e "${BLUE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                    INTERLOCK AI DEPLOYMENT                  ║
║                     Production Package                      ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

log "Starting Interlock AI deployment process..."

# Step 1: Pre-deployment checks
log "Step 1: Running pre-deployment checks..."
if [ -f "$DEPLOYMENT_DIR/pre-deployment-check.js" ]; then
    node "$DEPLOYMENT_DIR/pre-deployment-check.js" || error "Pre-deployment checks failed"
    success "Pre-deployment checks passed"
else
    warning "Pre-deployment check script not found, skipping..."
fi

# Step 2: Create backup
log "Step 2: Creating backup..."
if [ -d "$APP_DIR" ]; then
    mkdir -p "$BACKUP_DIR"
    cp -r "$APP_DIR" "$BACKUP_DIR/app_backup" 2>/dev/null || true
    success "Backup created at $BACKUP_DIR"
else
    log "No existing application found, skipping backup..."
fi

# Step 3: Prepare application directory
log "Step 3: Preparing application directory..."
mkdir -p "$APP_DIR"

# Step 4: Copy application files
log "Step 4: Copying application files..."
if [ -d "$DEPLOYMENT_DIR/../app" ]; then
    cp -r "$DEPLOYMENT_DIR/../"* "$APP_DIR/" 2>/dev/null || true
    # Exclude deployment directory from copy
    rm -rf "$APP_DIR/deployment" 2>/dev/null || true
    success "Application files copied"
else
    error "Application source files not found"
fi

# Step 5: Install production dependencies
log "Step 5: Installing production dependencies..."
cd "$APP_DIR"

# Use production package.json if available
if [ -f "$DEPLOYMENT_DIR/config/package.prod.json" ]; then
    cp "$DEPLOYMENT_DIR/config/package.prod.json" "$APP_DIR/package.json"
    log "Using production package.json"
fi

# Install dependencies with production flag
npm ci --production --silent || npm install --production --silent || error "Failed to install dependencies"
success "Dependencies installed"

# Step 6: Setup environment
log "Step 6: Setting up environment..."
if [ -f "$DEPLOYMENT_DIR/environment/.env.production" ]; then
    cp "$DEPLOYMENT_DIR/environment/.env.production" "$APP_DIR/.env"
    success "Production environment configured"
else
    warning "Production environment file not found, using existing .env if available"
fi

# Step 7: Build application
log "Step 7: Building application for production..."
if [ -f "$DEPLOYMENT_DIR/production-build.js" ]; then
    node "$DEPLOYMENT_DIR/production-build.js" || error "Production build failed"
else
    npm run build || error "Build failed"
fi
success "Application built successfully"

# Step 8: Setup database
log "Step 8: Setting up database..."
if [ -f "$DEPLOYMENT_DIR/scripts/database-setup.js" ]; then
    node "$DEPLOYMENT_DIR/scripts/database-setup.js" || warning "Database setup encountered issues"
    success "Database setup completed"
else
    warning "Database setup script not found, skipping..."
fi

# Step 9: Set file permissions
log "Step 9: Setting file permissions..."
find "$APP_DIR" -type f -exec chmod 644 {} \;
find "$APP_DIR" -type d -exec chmod 755 {} \;
chmod +x "$APP_DIR/server.js" 2>/dev/null || true
success "File permissions set"

# Step 10: Health check
log "Step 10: Running health check..."
if [ -f "$DEPLOYMENT_DIR/scripts/health-check.js" ]; then
    sleep 5  # Give the application time to start
    node "$DEPLOYMENT_DIR/scripts/health-check.js" || warning "Health check failed, but deployment continued"
    success "Health check completed"
else
    warning "Health check script not found, skipping..."
fi

# Deployment summary
echo -e "\n${GREEN}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                   DEPLOYMENT COMPLETED                      ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

success "Interlock AI deployed successfully!"
log "Application directory: $APP_DIR"
log "Backup location: $BACKUP_DIR"
log "Deployment log: $LOG_FILE"

echo -e "\n${YELLOW}Next Steps:${NC}"
echo "1. Configure your domain to point to the application"
echo "2. Set up SSL certificate in cPanel"
echo "3. Configure Node.js app in cPanel (if required)"
echo "4. Test the application at your domain"
echo "5. Verify admin panel access"

echo -e "\n${BLUE}Support:${NC}"
echo "- Documentation: $DEPLOYMENT_DIR/docs/"
echo "- Troubleshooting: $DEPLOYMENT_DIR/docs/troubleshooting.md"
echo "- Health check: node $DEPLOYMENT_DIR/scripts/health-check.js"

log "Deployment process completed successfully"
