#!/usr/bin/env node

/**
 * Production Build Script
 * Optimized build process for production deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}[${new Date().toISOString()}] ${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function error(message) {
  log(`❌ ${message}`, 'red');
  process.exit(1);
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function setupProductionEnvironment() {
  info('Setting up production environment...');
  
  // Set NODE_ENV to production
  process.env.NODE_ENV = 'production';
  
  // Optimize memory usage for build
  process.env.NODE_OPTIONS = '--max-old-space-size=2048';
  
  success('Production environment configured');
}

async function generatePrismaClient() {
  info('Generating Prisma client...');
  
  try {
    // Set Prisma environment variables for production
    process.env.PRISMA_SCHEMA_ENGINE_TYPE = 'binary';
    process.env.PRISMA_QUERY_ENGINE_TYPE = 'binary';
    process.env.PRISMA_CLI_QUERY_ENGINE_TYPE = 'binary';
    
    execSync('npx prisma generate', {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_OPTIONS: '--max-old-space-size=1024'
      }
    });
    
    success('Prisma client generated successfully');
  } catch (err) {
    error(`Failed to generate Prisma client: ${err.message}`);
  }
}

async function buildNextJsApplication() {
  info('Building Next.js application...');
  
  try {
    // Use production Next.js config if available
    const prodConfigPath = path.join(__dirname, 'config', 'next.config.prod.mjs');
    if (fs.existsSync(prodConfigPath)) {
      const targetPath = path.join(process.cwd(), 'next.config.mjs');
      fs.copyFileSync(prodConfigPath, targetPath);
      info('Using production Next.js configuration');
    }
    
    // Build the application
    execSync('npm run build', {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'production',
        NODE_OPTIONS: '--max-old-space-size=2048'
      }
    });
    
    success('Next.js application built successfully');
  } catch (err) {
    error(`Failed to build Next.js application: ${err.message}`);
  }
}

async function optimizeBuildOutput() {
  info('Optimizing build output...');
  
  try {
    const buildDir = path.join(process.cwd(), '.next');
    
    if (fs.existsSync(buildDir)) {
      // Remove unnecessary files
      const unnecessaryPaths = [
        path.join(buildDir, 'cache'),
        path.join(buildDir, 'trace')
      ];
      
      unnecessaryPaths.forEach(dirPath => {
        if (fs.existsSync(dirPath)) {
          fs.rmSync(dirPath, { recursive: true, force: true });
          info(`Removed: ${path.basename(dirPath)}`);
        }
      });
      
      success('Build output optimized');
    } else {
      warning('Build directory not found, skipping optimization');
    }
  } catch (err) {
    warning(`Build optimization failed: ${err.message}`);
  }
}

async function createProductionFiles() {
  info('Creating production files...');
  
  try {
    // Create server.js if it doesn't exist
    const serverJsPath = path.join(process.cwd(), 'server.js');
    if (!fs.existsSync(serverJsPath)) {
      const serverJsContent = `
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || 'localhost';
const port = process.env.PORT || 3000;

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(\`> Ready on http://\${hostname}:\${port}\`);
  });
});
`;
      fs.writeFileSync(serverJsPath, serverJsContent.trim());
      info('Created server.js');
    }
    
    // Create .htaccess for Apache servers
    const htaccessPath = path.join(process.cwd(), '.htaccess');
    if (!fs.existsSync(htaccessPath)) {
      const htaccessContent = `
# Next.js Static Files
RewriteEngine On

# Handle Next.js static files
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
`;
      fs.writeFileSync(htaccessPath, htaccessContent.trim());
      info('Created .htaccess');
    }
    
    success('Production files created');
  } catch (err) {
    warning(`Failed to create some production files: ${err.message}`);
  }
}

async function validateBuild() {
  info('Validating build...');
  
  try {
    const buildDir = path.join(process.cwd(), '.next');
    const requiredFiles = [
      path.join(buildDir, 'BUILD_ID'),
      path.join(buildDir, 'static'),
      path.join(buildDir, 'server')
    ];
    
    let allFilesExist = true;
    requiredFiles.forEach(file => {
      if (!fs.existsSync(file)) {
        error(`Missing required build file: ${file}`);
        allFilesExist = false;
      }
    });
    
    if (allFilesExist) {
      success('Build validation passed');
    } else {
      error('Build validation failed');
    }
  } catch (err) {
    error(`Build validation failed: ${err.message}`);
  }
}

async function main() {
  console.log('\n' + '='.repeat(60));
  console.log('🏗️  INTERLOCK AI - PRODUCTION BUILD');
  console.log('='.repeat(60));
  
  try {
    await setupProductionEnvironment();
    await generatePrismaClient();
    await buildNextJsApplication();
    await optimizeBuildOutput();
    await createProductionFiles();
    await validateBuild();
    
    console.log('\n' + '='.repeat(60));
    success('🎉 Production build completed successfully!');
    console.log('='.repeat(60));
    
  } catch (err) {
    error(`Production build failed: ${err.message}`);
  }
}

// Run the build process
main().catch(err => {
  error(`Build process failed: ${err.message}`);
});
