# Interlock AI - Production Deployment Package

This package contains everything needed to deploy the Interlock AI application to production via cPanel.

## 📦 Package Contents

```
deployment/
├── README.md                    # This file
├── deploy.sh                   # Main deployment script
├── pre-deployment-check.js     # Pre-deployment validation
├── production-build.js         # Production build script
├── cpanel-setup.md            # cPanel configuration guide
├── environment/
│   ├── .env.production        # Production environment template
│   └── .env.example          # Environment variables example
├── scripts/
│   ├── database-setup.js     # Database initialization
│   ├── health-check.js       # Post-deployment health check
│   └── backup-database.js    # Database backup utility
├── config/
│   ├── next.config.prod.mjs  # Production Next.js config
│   └── package.prod.json     # Production package.json
└── docs/
    ├── deployment-checklist.md
    ├── troubleshooting.md
    └── rollback-guide.md
```

## 🚀 Quick Deployment

### Prerequisites
- cPanel hosting account with Node.js support
- PostgreSQL database access
- SSH access (recommended) or File Manager access
- Domain/subdomain configured

### Step 1: Prepare Environment
1. Upload this deployment package to your server
2. Configure environment variables in `environment/.env.production`
3. Run pre-deployment checks

### Step 2: Deploy
```bash
# Make deployment script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

### Step 3: Verify
- Check application health at your domain
- Verify database connectivity
- Test admin panel access

## 📋 Deployment Checklist

- [ ] Environment variables configured
- [ ] Database created and accessible
- [ ] Domain/subdomain pointed to application
- [ ] SSL certificate installed
- [ ] Node.js version compatible (18+)
- [ ] File permissions set correctly
- [ ] Application starts successfully
- [ ] Database migrations completed
- [ ] Admin panel accessible

## 🔧 Configuration

### Environment Variables
Copy `environment/.env.example` to `environment/.env.production` and configure:

```env
# Database
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# Application
NODE_ENV=production
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-secret-key

# Admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-password
```

### cPanel Setup
See `cpanel-setup.md` for detailed cPanel configuration instructions.

## 🆘 Support

If you encounter issues:
1. Check `docs/troubleshooting.md`
2. Review deployment logs
3. Run health check: `node scripts/health-check.js`
4. Contact support with error details

## 📚 Additional Documentation

- [cPanel Setup Guide](cpanel-setup.md)
- [Deployment Checklist](docs/deployment-checklist.md)
- [Troubleshooting Guide](docs/troubleshooting.md)
- [Rollback Guide](docs/rollback-guide.md)

---

**Version**: 1.0.0  
**Last Updated**: $(date)  
**Support**: <EMAIL>
