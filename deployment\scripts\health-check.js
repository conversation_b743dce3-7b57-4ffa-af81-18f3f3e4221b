#!/usr/bin/env node

/**
 * Health Check Script
 * Verifies application health after deployment
 */

const http = require('http');
const https = require('https');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}[${new Date().toISOString()}] ${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function checkEnvironmentVariables() {
  info('Checking environment variables...');
  
  const requiredVars = [
    'DATABASE_URL',
    'NODE_ENV',
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET'
  ];
  
  let allVarsPresent = true;
  
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      success(`${varName} is configured`);
    } else {
      error(`${varName} is missing`);
      allVarsPresent = false;
    }
  });
  
  return allVarsPresent;
}

async function checkDatabaseConnection() {
  info('Testing database connection...');
  
  try {
    // Test database connection using Prisma
    execSync('npx prisma db execute --stdin', { 
      input: 'SELECT 1 as health_check;',
      stdio: 'pipe',
      timeout: 10000 
    });
    success('Database connection is healthy');
    return true;
  } catch (err) {
    error('Database connection failed');
    console.error('Database error:', err.message);
    return false;
  }
}

async function checkApplicationFiles() {
  info('Checking application files...');
  
  const requiredFiles = [
    'package.json',
    '.next/BUILD_ID',
    '.next/static',
    'node_modules'
  ];
  
  let allFilesPresent = true;
  
  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      success(`${file} exists`);
    } else {
      error(`${file} is missing`);
      allFilesPresent = false;
    }
  });
  
  return allFilesPresent;
}

async function checkHttpEndpoint(url, timeout = 10000) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: true,
          statusCode: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 500) // Limit data size
        });
      });
    });
    
    req.on('error', (err) => {
      resolve({
        success: false,
        error: err.message
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Request timeout'
      });
    });
    
    req.end();
  });
}

async function checkApplicationEndpoints() {
  info('Testing application endpoints...');
  
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const endpoints = [
    { path: '/', name: 'Homepage' },
    { path: '/api/health', name: 'Health API' },
    { path: '/admin', name: 'Admin Panel' }
  ];
  
  let allEndpointsHealthy = true;
  
  for (const endpoint of endpoints) {
    const url = `${baseUrl}${endpoint.path}`;
    info(`Testing ${endpoint.name}: ${url}`);
    
    try {
      const result = await checkHttpEndpoint(url);
      
      if (result.success && result.statusCode < 400) {
        success(`${endpoint.name} is accessible (${result.statusCode})`);
      } else if (result.success) {
        warning(`${endpoint.name} returned ${result.statusCode}`);
        if (result.statusCode === 404) {
          info('404 may be normal for some endpoints');
        } else {
          allEndpointsHealthy = false;
        }
      } else {
        error(`${endpoint.name} failed: ${result.error}`);
        allEndpointsHealthy = false;
      }
    } catch (err) {
      error(`${endpoint.name} test failed: ${err.message}`);
      allEndpointsHealthy = false;
    }
  }
  
  return allEndpointsHealthy;
}

async function checkSystemResources() {
  info('Checking system resources...');
  
  try {
    // Check memory usage
    const memUsage = process.memoryUsage();
    const memUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const memTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
    
    success(`Memory usage: ${memUsedMB}MB / ${memTotalMB}MB`);
    
    // Check disk space (basic check)
    const stats = fs.statSync(process.cwd());
    success('Disk access verified');
    
    // Check Node.js version
    success(`Node.js version: ${process.version}`);
    
    return true;
  } catch (err) {
    warning(`System resource check failed: ${err.message}`);
    return false;
  }
}

async function checkPrismaClient() {
  info('Checking Prisma client...');
  
  try {
    // Check if Prisma client is generated
    const prismaClientPath = path.join(process.cwd(), 'node_modules', '.prisma', 'client');
    if (fs.existsSync(prismaClientPath)) {
      success('Prisma client is generated');
      return true;
    } else {
      error('Prisma client not found');
      return false;
    }
  } catch (err) {
    error(`Prisma client check failed: ${err.message}`);
    return false;
  }
}

async function generateHealthReport() {
  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'unknown',
    nodeVersion: process.version,
    checks: {
      environment: false,
      database: false,
      files: false,
      endpoints: false,
      resources: false,
      prisma: false
    },
    overall: false
  };
  
  // Run all checks
  report.checks.environment = await checkEnvironmentVariables();
  report.checks.database = await checkDatabaseConnection();
  report.checks.files = await checkApplicationFiles();
  report.checks.endpoints = await checkApplicationEndpoints();
  report.checks.resources = await checkSystemResources();
  report.checks.prisma = await checkPrismaClient();
  
  // Calculate overall health
  const passedChecks = Object.values(report.checks).filter(Boolean).length;
  const totalChecks = Object.keys(report.checks).length;
  report.overall = passedChecks >= totalChecks - 1; // Allow one non-critical failure
  
  // Save report
  const reportPath = path.join(process.cwd(), 'health-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  info(`Health report saved to: ${reportPath}`);
  
  return report;
}

async function main() {
  console.log('\n' + '='.repeat(60));
  console.log('🏥 INTERLOCK AI - HEALTH CHECK');
  console.log('='.repeat(60));
  
  try {
    const report = await generateHealthReport();
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 HEALTH CHECK SUMMARY');
    console.log('='.repeat(60));
    
    Object.entries(report.checks).forEach(([check, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${check.toUpperCase().padEnd(15)} ${status}`);
    });
    
    console.log('\n' + '='.repeat(60));
    if (report.overall) {
      success('🎉 Overall Health: HEALTHY');
      console.log('Application is ready for production use!');
    } else {
      warning('⚠️  Overall Health: ISSUES DETECTED');
      console.log('Please review failed checks and resolve issues.');
    }
    console.log('='.repeat(60));
    
    // Exit with appropriate code
    process.exit(report.overall ? 0 : 1);
    
  } catch (err) {
    error(`Health check failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the health check
main().catch(err => {
  error(`Health check process failed: ${err.message}`);
  process.exit(1);
});
