# cPanel Setup Guide for Interlock AI

This guide walks you through setting up Interlock AI on a cPanel hosting environment.

## 📋 Prerequisites

- cPanel hosting account with:
  - Node.js support (version 18+)
  - PostgreSQL database access
  - SSH access (recommended)
  - SSL certificate capability
  - At least 1GB RAM and 2GB storage

## 🗄️ Database Setup

### Step 1: Create PostgreSQL Database

1. **Login to cPanel**
2. **Navigate to "PostgreSQL Databases"**
3. **Create a new database:**
   - Database Name: `interlock_ai_prod`
   - Click "Create Database"

4. **Create database user:**
   - Username: `interlock_user`
   - Password: Generate a strong password
   - Click "Create User"

5. **Add user to database:**
   - Select user: `interlock_user`
   - Select database: `interlock_ai_prod`
   - Grant ALL PRIVILEGES
   - Click "Add"

6. **Note connection details:**
   ```
   Host: localhost (or provided hostname)
   Port: 5432 (or provided port)
   Database: interlock_ai_prod
   Username: interlock_user
   Password: [your-password]
   ```

### Step 2: Configure Database URL

Create your DATABASE_URL in this format:
```
postgresql://username:password@hostname:port/database?sslmode=require
```

Example:
```
postgresql://interlock_user:your_password@localhost:5432/interlock_ai_prod?sslmode=require
```

## 🌐 Domain Configuration

### Step 1: Domain/Subdomain Setup

**Option A: Main Domain**
- Point your main domain to the application
- Application files go in `public_html/`

**Option B: Subdomain**
1. Create subdomain in cPanel (e.g., `app.yourdomain.com`)
2. Application files go in `public_html/app/`

### Step 2: SSL Certificate

1. **Navigate to "SSL/TLS" in cPanel**
2. **Choose certificate option:**
   - Let's Encrypt (free, recommended)
   - Upload custom certificate
   - Use hosting provider's certificate

3. **Enable "Force HTTPS Redirect"**

## ⚙️ Node.js Configuration

### Step 1: Enable Node.js

1. **Navigate to "Node.js" in cPanel**
2. **Create Node.js App:**
   - Node.js Version: 18+ (latest LTS recommended)
   - Application Mode: Production
   - Application Root: `/public_html` (or subdomain path)
   - Application URL: Your domain/subdomain
   - Application Startup File: `server.js`

3. **Set Environment Variables:**
   ```
   NODE_ENV=production
   PORT=3000
   ```

### Step 2: Configure Application

1. **Set Node.js version** (if not done above)
2. **Install dependencies** will be handled by deployment script
3. **Configure startup file** as `server.js`

## 📁 File Structure

After deployment, your file structure should look like:

```
public_html/
├── .env                    # Environment variables
├── .htaccess              # Apache configuration
├── server.js              # Node.js server entry point
├── package.json           # Dependencies
├── next.config.mjs        # Next.js configuration
├── app/                   # Next.js app directory
├── components/            # React components
├── lib/                   # Utility libraries
├── prisma/               # Database schema
├── .next/                # Built application (generated)
└── node_modules/         # Dependencies (generated)
```

## 🔐 Environment Variables

### Required Variables

Create `.env` file in your application root with:

```env
# Database
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# Application
NODE_ENV=production
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-super-secret-key-here

# Admin Access
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-admin-password

# Optional: Email Configuration
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password
```

### Security Notes

- Use strong, unique passwords
- Keep your NEXTAUTH_SECRET secure and random
- Never commit `.env` files to version control
- Regularly rotate passwords and secrets

## 🚀 Deployment Process

### Method 1: Using Deployment Script (Recommended)

1. **Upload deployment package** to your server
2. **SSH into your server** (or use cPanel Terminal)
3. **Navigate to deployment directory**
4. **Run deployment:**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

### Method 2: Manual Deployment

1. **Upload application files** via File Manager or FTP
2. **Install dependencies:**
   ```bash
   cd public_html
   npm install --production
   ```
3. **Build application:**
   ```bash
   npm run build
   ```
4. **Start application** via Node.js interface

## 🔧 cPanel Configuration

### File Manager Settings

1. **Set file permissions:**
   - Directories: 755
   - Files: 644
   - Executable files: 755

2. **Upload limits:**
   - Increase if needed for large deployments
   - Check with hosting provider

### Cron Jobs (Optional)

Set up maintenance tasks:

```bash
# Database backup (daily at 2 AM)
0 2 * * * cd /home/<USER>/public_html && node scripts/backup-database.js

# Health check (every 5 minutes)
*/5 * * * * cd /home/<USER>/public_html && node scripts/health-check.js
```

## 📊 Monitoring

### Application Logs

- **cPanel Error Logs:** Check for application errors
- **Node.js Logs:** Available in Node.js interface
- **Custom Logs:** Application generates logs in `logs/` directory

### Health Checks

Run health check script:
```bash
node scripts/health-check.js
```

### Database Monitoring

Access database diagnostics at:
```
https://yourdomain.com/admin/cms
```
Navigate to "Database" tab.

## 🆘 Troubleshooting

### Common Issues

1. **Application won't start:**
   - Check Node.js version compatibility
   - Verify environment variables
   - Check file permissions

2. **Database connection failed:**
   - Verify DATABASE_URL format
   - Check database credentials
   - Ensure database exists

3. **Build errors:**
   - Check available memory
   - Verify all dependencies installed
   - Check for syntax errors

4. **SSL issues:**
   - Verify certificate installation
   - Check NEXTAUTH_URL matches domain
   - Ensure HTTPS redirect enabled

### Getting Help

1. **Check logs** in cPanel Error Logs
2. **Run diagnostics** using provided scripts
3. **Contact hosting support** for server-specific issues
4. **Review documentation** in `docs/` directory

## 📞 Support

- **Documentation:** `/deployment/docs/`
- **Health Check:** `node scripts/health-check.js`
- **Database Diagnostics:** Admin panel → Database tab
- **Email:** <EMAIL>

---

**Next Steps:** After completing this setup, run the deployment script and verify your application is accessible at your domain.
