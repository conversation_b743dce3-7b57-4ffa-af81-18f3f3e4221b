"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import {
  getFeatureCards,
  getResearchAreas,
  getTeamMembers,
  getJobPostings,
  getPageContent,
  getFooterPages,
  deleteFeatureCard,
  deleteResearchArea,
  deleteTeamMember,
  deleteJobPosting,
  deleteFooterPage,
  type FeatureCard,
  type ResearchArea,
  type TeamMember,
  type JobPosting,
  type PageContent,
  type FooterPage
} from "@/lib/api-client"
import { FeatureCardForm } from "@/components/feature-card-form"
import { ResearchForm } from "@/components/research-form"
import { TeamMemberForm } from "@/components/team-member-form"
import { PageContentForm } from "@/components/page-content-form"
import { JobPostingForm } from "@/components/job-posting-form"
import { FooterPageForm } from "@/components/footer-page-form"
import { Trash2, Edit, Plus, Eye } from "lucide-react"

export default function CMSPage() {
  const [featureCards, setFeatureCards] = useState<FeatureCard[]>([])
  const [researchAreas, setResearchAreas] = useState<ResearchArea[]>([])
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([])
  const [pageContent, setPageContent] = useState<PageContent[]>([])
  const [footerPages, setFooterPages] = useState<FooterPage[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("homepage")
  const [editingItem, setEditingItem] = useState<any>(null)
  const [editingType, setEditingType] = useState<string>("")
  const [showForm, setShowForm] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadAllData()
  }, [])

  const loadAllData = async () => {
    try {
      setLoading(true)
      const [cards, research, team, jobs, content, footer] = await Promise.all([
        getFeatureCards(),
        getResearchAreas(),
        getTeamMembers(),
        getJobPostings(),
        getPageContent(),
        getFooterPages()
      ])

      setFeatureCards(cards)
      setResearchAreas(research)
      setTeamMembers(team)
      setJobPostings(jobs)
      setPageContent(content)
      setFooterPages(footer)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (item: any, type: string) => {
    setEditingItem(item)
    setEditingType(type)
    setShowForm(true)
  }

  const handleAdd = (type: string) => {
    setEditingItem(null)
    setEditingType(type)
    setShowForm(true)
  }

  const handleDelete = async (id: number, type: string) => {
    if (!confirm("Are you sure you want to delete this item?")) return

    try {
      let success = false
      switch (type) {
        case "feature":
          success = await deleteFeatureCard(id)
          break
        case "research":
          success = await deleteResearchArea(id)
          break
        case "team":
          success = await deleteTeamMember(id)
          break
        case "job":
          success = await deleteJobPosting(id)
          break
        case "footer":
          success = await deleteFooterPage(id)
          break
      }

      if (success) {
        toast({
          title: "Success",
          description: "Item deleted successfully",
        })
        loadAllData()
      } else {
        throw new Error("Delete failed")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete item",
        variant: "destructive",
      })
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingItem(null)
    setEditingType("")
    loadAllData()
  }

  const renderForm = () => {
    if (!showForm) return null

    switch (editingType) {
      case "feature":
        return (
          <FeatureCardForm
            initialData={editingItem}
            mode={editingItem ? "edit" : "create"}
            onSuccess={handleFormSuccess}
          />
        )
      case "research":
        return (
          <ResearchForm
            initialData={editingItem}
            mode={editingItem ? "edit" : "create"}
            onSuccess={handleFormSuccess}
          />
        )
      case "team":
        return (
          <TeamMemberForm
            initialData={editingItem}
            mode={editingItem ? "edit" : "add"}
            onSuccess={handleFormSuccess}
          />
        )
      case "content":
        return (
          <PageContentForm
            initialData={editingItem}
            mode={editingItem ? "edit" : "add"}
            onSuccess={handleFormSuccess}
          />
        )
      case "job":
        return (
          <JobPostingForm
            initialData={editingItem}
            mode={editingItem ? "edit" : "create"}
            onSuccess={handleFormSuccess}
          />
        )
      case "footer":
        return (
          <FooterPageForm
            initialData={editingItem}
            mode={editingItem ? "edit" : "create"}
            onSuccess={handleFormSuccess}
          />
        )
      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="w-8 h-8 rounded-full border-2 border-t-transparent border-cyan-400 animate-spin mx-auto mb-4"></div>
            <p className="text-gray-400">Loading CMS...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-7xl mx-auto"
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
              Content Management System
            </h1>
            <p className="text-gray-400 mt-2">Manage all website content from one place</p>
          </div>
          <Button
            variant="outline"
            onClick={() => window.open('/', '_blank')}
            className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10"
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview Website
          </Button>
        </div>

        {showForm && (
          <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl text-cyan-400">
                  {editingItem ? "Edit" : "Add"} {editingType.charAt(0).toUpperCase() + editingType.slice(1)}
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowForm(false)}
                  className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                >
                  Cancel
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {renderForm()}
            </CardContent>
          </Card>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-8 bg-gray-800/50">
            <TabsTrigger value="homepage">Homepage</TabsTrigger>
            <TabsTrigger value="research">Research</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="content">Pages</TabsTrigger>
            <TabsTrigger value="jobs">Jobs</TabsTrigger>
            <TabsTrigger value="footer-research">Research Pages</TabsTrigger>
            <TabsTrigger value="footer-company">Company Pages</TabsTrigger>
            <TabsTrigger value="footer-legal">Legal Pages</TabsTrigger>
          </TabsList>

          <TabsContent value="homepage" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Feature Cards</CardTitle>
                    <CardDescription>Manage homepage feature cards</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("feature")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Feature
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {featureCards.map((card) => (
                    <Card key={card.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center space-x-2">
                            <span className="text-2xl">{card.icon}</span>
                            <CardTitle className="text-lg text-white">{card.title}</CardTitle>
                          </div>
                          <Badge variant="secondary">{card.order_index}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-gray-400 text-sm mb-4 line-clamp-2">{card.description}</p>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(card, "feature")}
                            className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(card.id, "feature")}
                            className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="research" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Research Areas</CardTitle>
                    <CardDescription>Manage research projects and areas</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("research")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Research
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {researchAreas.map((area) => (
                    <Card key={area.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg text-white">{area.title}</CardTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant={area.status === "published" ? "default" : "secondary"}>
                                {area.status}
                              </Badge>
                              <Badge variant="outline">{area.category}</Badge>
                              <span className="text-sm text-gray-400">Lead: {area.lead}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(area, "research")}
                              className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(area.id, "research")}
                              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-400 text-sm">{area.summary}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Team Members</CardTitle>
                    <CardDescription>Manage team member profiles</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("team")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Member
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {teamMembers.map((member) => (
                    <Card key={member.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-cyan-500 to-purple-600 flex items-center justify-center text-white font-semibold">
                              {member.initials}
                            </div>
                            <div>
                              <CardTitle className="text-lg text-white">{member.name}</CardTitle>
                              <p className="text-sm text-gray-400">{member.position}</p>
                            </div>
                          </div>
                          <Badge variant="secondary">{member.order_index}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-gray-400 text-sm mb-4 line-clamp-2">{member.bio}</p>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(member, "team")}
                            className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(member.id, "team")}
                            className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="content" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Page Content</CardTitle>
                    <CardDescription>Manage content across all pages</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("content")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Content
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pageContent.map((content) => (
                    <Card key={content.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg text-white">{content.title}</CardTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant="outline">{content.page_id}</Badge>
                              <Badge variant="secondary">{content.section_id}</Badge>
                              <Badge variant={content.is_active ? "default" : "destructive"}>
                                {content.is_active ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(content, "content")}
                              className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(content.id, "content")}
                              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-400 text-sm line-clamp-2">{content.content}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="jobs" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Job Postings</CardTitle>
                    <CardDescription>Manage career opportunities</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("job")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Job
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {jobPostings.map((job) => (
                    <Card key={job.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg text-white">{job.title}</CardTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant="outline">{job.department}</Badge>
                              <Badge variant="secondary">{job.type}</Badge>
                              <span className="text-sm text-gray-400">{job.location}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(job, "job")}
                              className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(job.id, "job")}
                              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-400 text-sm line-clamp-2">{job.description}</p>
                        <div className="mt-2">
                          <p className="text-xs text-gray-500">
                            Posted: {new Date(job.posted_date).toLocaleDateString()}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="footer-research" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Research Pages</CardTitle>
                    <CardDescription>Manage research-related footer pages</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("footer")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Research Page
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {footerPages.filter(page => page.category === 'research').map((page) => (
                    <Card key={page.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg text-white">{page.title}</CardTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant={page.is_published ? "default" : "secondary"}>
                                {page.is_published ? "Published" : "Draft"}
                              </Badge>
                              <Badge variant="outline">/{page.category}/{page.slug}</Badge>
                              <span className="text-sm text-gray-400">Order: {page.order_index}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(page, "footer")}
                              className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(page.id, "footer")}
                              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-400 text-sm line-clamp-2">{page.content.substring(0, 150)}...</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="footer-company" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Company Pages</CardTitle>
                    <CardDescription>Manage company-related footer pages</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("footer")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Company Page
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {footerPages.filter(page => page.category === 'company').map((page) => (
                    <Card key={page.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg text-white">{page.title}</CardTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant={page.is_published ? "default" : "secondary"}>
                                {page.is_published ? "Published" : "Draft"}
                              </Badge>
                              <Badge variant="outline">/{page.category}/{page.slug}</Badge>
                              <span className="text-sm text-gray-400">Order: {page.order_index}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(page, "footer")}
                              className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(page.id, "footer")}
                              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-400 text-sm line-clamp-2">{page.content.substring(0, 150)}...</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="footer-legal" className="space-y-6">
            <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl text-cyan-400">Legal Pages</CardTitle>
                    <CardDescription>Manage legal-related footer pages</CardDescription>
                  </div>
                  <Button
                    onClick={() => handleAdd("footer")}
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Legal Page
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {footerPages.filter(page => page.category === 'legal').map((page) => (
                    <Card key={page.id} className="border-gray-700 bg-gray-800/50">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg text-white">{page.title}</CardTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant={page.is_published ? "default" : "secondary"}>
                                {page.is_published ? "Published" : "Draft"}
                              </Badge>
                              <Badge variant="outline">/{page.category}/{page.slug}</Badge>
                              <span className="text-sm text-gray-400">Order: {page.order_index}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(page, "footer")}
                              className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(page.id, "footer")}
                              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-400 text-sm line-clamp-2">{page.content.substring(0, 150)}...</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}
