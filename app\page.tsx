"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CircuitAnimation } from "@/components/circuit-animation"
import { OptimizedImage } from "@/components/optimized-image"
import { getFeatureCards, type FeatureCard } from "@/lib/api-client"
import { LoadingSpinner } from "@/components/loading-spinner"
import { BrainAnimation } from "@/components/brain-animation"
import { CyberpunkEffects } from "@/components/cyberpunk-effects"

export default function Home() {
  const [featureCards, setFeatureCards] = useState<FeatureCard[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadFeatureCards() {
      try {
        const cards = await getFeatureCards()
        console.log("Feature cards loaded:", cards)
        setFeatureCards(cards)
      } catch (error) {
        console.error("Failed to load feature cards:", error)
      } finally {
        setLoading(false)
      }
    }

    loadFeatureCards()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
      {/* Background Animations */}
      <BrainAnimation />
      <CyberpunkEffects />

      <div className="container mx-auto px-4 relative z-10">
        {/* Hero Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 z-0 opacity-30">
            <CircuitAnimation />
          </div>

          <div className="relative z-10 max-w-4xl mx-auto text-center">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
              <h1 className="text-4xl md:text-6xl font-orbitron font-black tracking-wider uppercase bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 mb-6 drop-shadow-[0_0_15px_rgba(34,211,238,0.5)] hover:drop-shadow-[0_0_25px_rgba(34,211,238,0.8)] transition-all duration-300">
                <span className="inline-block hover:scale-105 transition-transform duration-300">Artificial</span>{" "}
                <span className="inline-block hover:scale-105 transition-transform duration-300 delay-75">Human-Inspired</span>{" "}
                <span className="inline-block hover:scale-105 transition-transform duration-300 delay-150">Learning</span>
              </h1>

              <p className="text-xl md:text-2xl mb-8 text-gray-300">
                Pioneering the next generation of AI through human-inspired cognitive models and neural associative
                frameworks.
              </p>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link href="/research">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white font-medium px-8 py-6 rounded-md shadow-[0_0_15px_rgba(124,58,237,0.5)] hover:shadow-[0_0_25px_rgba(124,58,237,0.7)] transition-all duration-300"
                  >
                    Explore Our Research
                  </Button>
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          {loading ? (
            <LoadingSpinner />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {featureCards.map((feature) => (
                <motion.div
                  key={feature.id}
                  className="bg-gray-900/50 backdrop-blur-sm border border-purple-500/20 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-300 shadow-[0_0_15px_rgba(124,58,237,0.1)] hover:shadow-[0_0_25px_rgba(124,58,237,0.2)]"
                  whileHover={{ y: -5 }}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-bold mb-2 text-cyan-400">{feature.title}</h3>
                  <p className="text-gray-400">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          )}
        </section>

        {/* About Section */}
        <section className="py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-cyan-400">
                Redefining AI Through Human Cognition
              </h2>
              <p className="text-lg text-gray-300 mb-6">
                At Interlock LLC, we're not just building artificial intelligence—we're creating systems that learn,
                adapt, and reason with human-inspired cognitive frameworks.
              </p>
              <p className="text-lg text-gray-300">
                Our interdisciplinary team combines expertise in neuroscience, computer science, and cognitive psychology
                to develop AI that understands context, forms meaningful associations, and prioritizes information like
                humans do.
              </p>
            </motion.div>

            <motion.div
              className="relative h-80 rounded-xl overflow-hidden border border-purple-500/30 shadow-[0_0_30px_rgba(168,85,247,0.2)]"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/60 to-cyan-900/80 z-10"></div>
              <div className="absolute inset-0 z-0">
                <OptimizedImage
                  src="/placeholder.svg?height=600&width=800"
                  alt="AI Research Visualization"
                  width={800}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute inset-0 z-20 flex items-center justify-center">
                <div className="w-20 h-20 rounded-full bg-purple-500/20 backdrop-blur-sm flex items-center justify-center border border-purple-500/50">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-purple-600 flex items-center justify-center text-white text-4xl">
                    AI
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </div>
  )
}
