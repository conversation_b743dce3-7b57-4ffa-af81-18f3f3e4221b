# Deployment Checklist for Interlock AI

Use this checklist to ensure a successful production deployment.

## 📋 Pre-Deployment Checklist

### Environment Setup
- [ ] **cPanel account ready** with Node.js support (v18+)
- [ ] **PostgreSQL database created** and accessible
- [ ] **Domain/subdomain configured** and pointing to hosting
- [ ] **SSL certificate installed** and working
- [ ] **Environment variables configured** in `.env.production`
- [ ] **Database URL tested** and connection verified

### Application Preparation
- [ ] **Code tested locally** and all features working
- [ ] **Database schema finalized** and migrations ready
- [ ] **Dependencies updated** and security vulnerabilities addressed
- [ ] **Build process tested** locally
- [ ] **Admin credentials configured** for production access

### Security Configuration
- [ ] **Strong passwords set** for all accounts
- [ ] **NEXTAUTH_SECRET generated** (32+ characters)
- [ ] **Database credentials secured** and not in version control
- [ ] **CORS origins configured** properly
- [ ] **Rate limiting configured** if needed

## 🚀 Deployment Process

### Step 1: Upload Deployment Package
- [ ] **Upload deployment files** to server via FTP/File Manager
- [ ] **Set file permissions** correctly (755 for directories, 644 for files)
- [ ] **Verify deployment script** is executable (`chmod +x deploy.sh`)

### Step 2: Configure Environment
- [ ] **Copy `.env.production`** to application directory as `.env`
- [ ] **Update all placeholder values** with actual production values
- [ ] **Verify DATABASE_URL format** is correct
- [ ] **Test environment variables** are loaded correctly

### Step 3: Run Deployment
- [ ] **Execute pre-deployment check** (`node pre-deployment-check.js`)
- [ ] **Run deployment script** (`./deploy.sh`)
- [ ] **Monitor deployment logs** for any errors
- [ ] **Verify build completion** successfully

### Step 4: Database Setup
- [ ] **Database migrations applied** successfully
- [ ] **Prisma client generated** without errors
- [ ] **Database seeding completed** (if applicable)
- [ ] **Admin user created** or configured

## ✅ Post-Deployment Verification

### Application Health
- [ ] **Homepage loads** correctly at your domain
- [ ] **All main pages accessible** and rendering properly
- [ ] **Admin panel accessible** at `/admin`
- [ ] **Database diagnostics working** in admin panel
- [ ] **API endpoints responding** correctly

### Performance & Security
- [ ] **HTTPS working** and redirecting from HTTP
- [ ] **SSL certificate valid** and not expired
- [ ] **Page load times acceptable** (< 3 seconds)
- [ ] **Mobile responsiveness** working correctly
- [ ] **Security headers present** in response

### Functionality Testing
- [ ] **Contact forms working** (if applicable)
- [ ] **User authentication working** (if applicable)
- [ ] **Database operations working** (create, read, update, delete)
- [ ] **File uploads working** (if applicable)
- [ ] **Email notifications working** (if configured)

### Monitoring Setup
- [ ] **Health check script working** (`node scripts/health-check.js`)
- [ ] **Error logging configured** and accessible
- [ ] **Performance monitoring** set up (if applicable)
- [ ] **Backup system configured** (if applicable)
- [ ] **Uptime monitoring** configured (if applicable)

## 🔧 cPanel Configuration

### Node.js App Settings
- [ ] **Node.js version set** to 18+ (latest LTS recommended)
- [ ] **Application mode** set to "Production"
- [ ] **Startup file** set to `server.js`
- [ ] **Environment variables** configured in cPanel
- [ ] **Application restarted** after configuration changes

### Domain & SSL
- [ ] **Domain pointing** to correct directory
- [ ] **SSL certificate** installed and active
- [ ] **Force HTTPS redirect** enabled
- [ ] **WWW redirect** configured (if needed)

### Database Configuration
- [ ] **PostgreSQL database** created and accessible
- [ ] **Database user** created with appropriate permissions
- [ ] **Connection string** tested and working
- [ ] **Database backups** configured (if available)

## 🆘 Troubleshooting Checklist

### If Application Won't Start
- [ ] **Check Node.js version** compatibility
- [ ] **Verify environment variables** are set correctly
- [ ] **Check file permissions** (755 for directories, 644 for files)
- [ ] **Review error logs** in cPanel
- [ ] **Verify all dependencies** are installed

### If Database Connection Fails
- [ ] **Verify DATABASE_URL format** is correct
- [ ] **Check database credentials** are accurate
- [ ] **Ensure database exists** and is accessible
- [ ] **Test connection** from cPanel database tools
- [ ] **Check firewall settings** (if applicable)

### If Pages Don't Load
- [ ] **Check domain configuration** and DNS settings
- [ ] **Verify SSL certificate** is working
- [ ] **Check .htaccess file** for redirect issues
- [ ] **Review server error logs** for details
- [ ] **Test with different browsers** and devices

## 📞 Support Resources

### Documentation
- [ ] **Deployment README** reviewed and followed
- [ ] **cPanel setup guide** completed
- [ ] **Troubleshooting guide** consulted for issues
- [ ] **Environment variables guide** referenced

### Health Checks
- [ ] **Pre-deployment check** passed
- [ ] **Post-deployment health check** passed
- [ ] **Database diagnostics** showing healthy status
- [ ] **Application monitoring** active

### Backup & Recovery
- [ ] **Backup created** before deployment
- [ ] **Rollback plan** prepared (if needed)
- [ ] **Database backup** available
- [ ] **Recovery procedures** documented

## 🎉 Deployment Complete

Once all items are checked:

1. **Document deployment details** (date, version, any issues encountered)
2. **Notify stakeholders** that application is live
3. **Monitor application** for first 24-48 hours
4. **Schedule regular health checks** and maintenance
5. **Plan for future updates** and improvements

---

**Deployment Date**: _______________  
**Deployed By**: _______________  
**Version**: _______________  
**Notes**: _______________
